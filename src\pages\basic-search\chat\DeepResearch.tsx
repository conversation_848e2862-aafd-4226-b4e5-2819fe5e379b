/* eslint-disable @typescript-eslint/no-explicit-any */
// import { useTranslation } from 'react-i18next'
import BasicSearchLayout from '../BasicSearchLayout'
import { useRouter } from 'next/router'
// import { Namespace } from '@/i18n'
import { useEffect } from 'react'
import { Text, TextEnum } from '@/components/business/text'
import { useDeepResearchStore } from '@/store/deep-research'
import useWebSocketWithReconnection from '@/pages/deep-explore/socket'
const DeepResearch = () => {
  const router = useRouter()
  // const { t } = useTranslation([Namespace.BASIC_SEARCH, Namespace.GLOBAL])
  const { taskId } = router.query
  const { getTaskStatus, taskStatus } = useDeepResearchStore()
  // 初始化获取详情得到大纲相关信息，并且链接websocket
  const initStatus = async (id: string) => {
    if (taskId) {
      await getTaskStatus(id)
    }
  }
  const handleSocketMessage = (message: any) => {
    console.log('message=====121231>', message)
  }
  const { startWebSocket } = useWebSocketWithReconnection({
    onMessage: handleSocketMessage,
  })
  useEffect(() => {
    startWebSocket()
    if (taskId) {
      initStatus(taskId as string)
    }
  }, [taskId])
  const outlineRender = (item: any) => {
    return (
      <div key={item.id}>
        <Text type={TextEnum.Body_medium} className='my-2 rounded-md bg-card px-4 py-3'>
          {item.sequence} {item.title}
        </Text>
        {item.children.map((child: any) => outlineRender(child))}
      </div>
    )
  }
  return (
    <BasicSearchLayout className='h-full'>
      <div className='flex h-full flex-col'>
        <header className='bg-card px-10 pb-6 pt-8'>
          <Text type={TextEnum.H2} className='!text-[20px]'>
            {taskStatus.task?.question}
          </Text>
          <Text type={TextEnum.Body_medium} className='text-secondary-black-3'>
            {taskStatus.task?.requirement}
          </Text>
        </header>
        <main className='mx-3 mt-3 flex h-full gap-4 overflow-hidden'>
          <div className='flex-1 pl-10 pt-8'>
            <Text type={TextEnum.H5}>Research Process</Text>
            <div
              className='h-[calc(100%-30px)] overflow-auto pr-2'
              style={{ scrollbarGutter: 'stable' }}>
              {taskStatus.task?.outline.map((item: any) => outlineRender(item))}
            </div>
          </div>
          <div
            style={{
              boxShadow: '0px 0px 35px -3px rgba(0,0,0,0.1),0px 4px 6px -4px rgba(0,0,0,0.15);',
            }}
            className='flex-1 rounded-lg bg-card px-10 py-8'>
            {/* 失败时显示 */}
            生成的草稿文档
          </div>
        </main>
      </div>
    </BasicSearchLayout>
  )
}
export default DeepResearch
