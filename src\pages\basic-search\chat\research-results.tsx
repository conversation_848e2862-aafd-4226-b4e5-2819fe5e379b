import React, { useEffect, useState, useRef } from 'react'
import { useRout<PERSON> } from 'next/router'
import Head from 'next/head'
import SimpleLayout from '@/components/layouts/SimpleLayout'
import { useTranslation } from 'react-i18next'
import ResearchMarkdownRenderer from '@/components/common/ResearchMarkdownRenderer'
import Split from 'react-split'
import useWebSocketWithReconnection, { SocketMessage } from '@/lib/hooks/socket'
import authRequest from '@/lib/authRequest'
import { isJsonStr } from '@/utils/isJson'
import { Namespace } from '@/i18n'
import { useDownloadMarkdown } from '@/utils/useDownloadMarkdown'
import PaddlePay from '@/components/pricing'
import { useMediaQuery } from 'react-responsive'

// 从环境变量中获取API基础URL，如果不存在则使用默认值
const API_BASE_URL = process.env.NEXT_PUBLIC_AUTH_API_URL || 'http://localhost:8000'

// 自定义CSS动画样式
const fadeInAnimation = `
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}
.animate-fadeIn {
  animation: fadeIn 0.3s ease-out forwards;
}
`

// 定义大纲项的类型
interface OutlineItem {
  id: string
  title: string
  description: string
  chapterIndex: string
  level: string
  children: OutlineItem[]
}

// 定义研究消息类型
interface ResearchMessage {
  ROLE: string
  TYPE: string
  STEP: string
  CONTENT: {
    ACTION?: string
    CHAPTER?: string
    MESSAGE?: string
  }
}

// 定义新的章节内容项类型，可以是普通消息或Markdown内容
interface ChapterContentItem {
  type: 'message' | 'markdown'
  message?: ResearchMessage
  content?: string
  isStreaming?: boolean
}

// 添加提示条组件
const DraftNotice: React.FC = () => {
  const { t } = useTranslation(Namespace.GLOBAL)
  const router = useRouter()
  const [showToast, setShowToast] = useState(false)

  return (
    <div className='ml-4 mr-4 rounded-lg border border-indigo-100 bg-indigo-50 p-4'>
      <div className='flex flex-col gap-2 md:flex-row md:items-center'>
        <div className='flex items-center space-x-3'>
          <div className='flex-shrink-0'>
            <svg
              className='h-5 w-5 text-indigo-600'
              xmlns='http://www.w3.org/2000/svg'
              viewBox='0 0 20 20'
              fill='currentColor'>
              <path
                fillRule='evenodd'
                d='M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z'
                clipRule='evenodd'
              />
            </svg>
          </div>
          <p className='text-sm text-indigo-700'>{t('researchResults.draftNotice')}</p>
        </div>
        <div className='md:ml-auto'>
          {' '}
          {/* 添加 md:ml-auto 使按钮在 web 端右对齐 */}
          <button
            onClick={() => {
              setShowToast(true)
              window.open('https://ai-smarties.com/deep-research')
              setTimeout(() => {
                setShowToast(false)
              }, 3000)
            }}
            className='inline-flex w-full items-center justify-center rounded-md border border-transparent bg-primary px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 md:w-auto'>
            {t('researchResults.tryCompleteReport')}
          </button>
        </div>
      </div>
      {/* {showToast && (
        <div className='animate-fadeIn fixed bottom-4 right-4 z-50 flex items-center rounded-lg border border-indigo-200 bg-indigo-50 p-4 shadow-lg'> */}
      {/* <div className='mr-3 flex-shrink-0'>
            <svg
              className='h-6 w-6 text-indigo-600'
              xmlns='http://www.w3.org/2000/svg'
              fill='none'
              viewBox='0 0 24 24'
              stroke='currentColor'>
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M13 10V3L4 14h7v7l9-11h-7z'
              />
            </svg>
          </div> */}
      {/* <div className='text-sm font-medium text-indigo-800'>
            <span className='font-bold'>{t('researchResults.comingSoon.title')}</span>
            <p className='mt-1 text-indigo-600'>{t('researchResults.comingSoon.description')}</p>
          </div> */}
      {/* </div>
      )} */}
    </div>
  )
}

const ResearchResultsPage: React.FC = () => {
  const { t } = useTranslation(Namespace.GLOBAL)
  const router = useRouter()
  const [researchData, setResearchData] = useState<any | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  // 新增全局loading状态控制
  const [isGlobalLoading, setIsGlobalLoading] = useState<boolean>(false)
  // 新增toast提示状态
  const [showToast, setShowToast] = useState<boolean>(false)
  // 添加用于判断是否来自深度研究页面的状态
  const [isFromDeepResearch, setIsFromDeepResearch] = useState<boolean>(true)

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [activeTask, setActiveTask] = useState<string>('')
  // 添加研究状态跟踪
  const [researchStatus, setResearchStatus] = useState<string>('START')
  const [outline, setOutline] = useState<OutlineItem[]>([])
  const [researchMessages, setResearchMessages] = useState<ResearchMessage[]>([])

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [streamingComplete, setStreamingComplete] = useState<boolean>(false)
  const [debug, setDebug] = useState<boolean>(false)
  // 章节相关状态
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [chapterContents, setChapterContents] = useState<Map<string, ResearchMessage[]>>(new Map())
  // 新增章节内容项状态，替代原来的chapterMarkdowns
  const [chapterContentItems, setChapterContentItems] = useState<Map<string, ChapterContentItem[]>>(
    new Map(),
  )
  // 只保留活跃状态信息
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [chapterMarkdownActive, setChapterMarkdownActive] = useState<Map<string, boolean>>(
    new Map(),
  )
  // 研究报告相关状态
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [reportContent, setReportContent] = useState<string>('')
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [isReportStreaming, setIsReportStreaming] = useState<boolean>(false)
  const [isFailed, setIsFailed] = useState<boolean>(false)
  const [LD, setLD] = useState(false)
  // 使用媒体查询判断是否为移动设备
  const isMobile = useMediaQuery({ maxWidth: 768 })
  // 添加请求跟踪引用，防止重复请求
  const requestSentRef = useRef<boolean>(false)
  const streamRequestSentRef = useRef<boolean>(false)
  const outlineRef = useRef<OutlineItem[]>([])
  const paddlePayRef = useRef<any>(null)
  const researchDataRef = useRef<{ task_id: string; question: string; requirement: string } | null>(
    null,
  )
  const contentRef = useRef<HTMLDivElement>(null)

  const handleSocketMessage = (message: SocketMessage) => {
    try {
      setLD(true)
      if (message.type === 'confirm') {
        const outline = message.data.message['报告大纲']
        setOutline(outline)
        outlineRef.current = outline
        return
      }
      if (
        !researchDataRef.current ||
        !researchDataRef.current?.task_id ||
        outlineRef.current.length === 0 ||
        !isJsonStr(message.data) ||
        message.task_id !== researchDataRef.current?.task_id
      ) {
        return
      }
      streamRequestSentRef.current = true
      console.log(
        `${new Date()} receive task result socket message, task_id: ${researchDataRef.current.task_id}`,
      )
      setActiveTask(t('researchResults.processingWait'))
      setIsGlobalLoading(true) // 开始流式请求时激活全局loading

      setResearchStatus('START') // 初始化研究状态

      // 提取JSON部分
      const messageData = JSON.parse(message.data)

      // 立即处理状态消息
      if (messageData.ROLE === 'WORKFLOW' && messageData.TYPE === 'STATUS' && messageData.STEP) {
        const newStep = messageData.STEP

        // 每个状态转换都需要保持loading状态，除非是END
        if (newStep === 'END') {
          // 研究结束时关闭loading并显示toast
          setIsGlobalLoading(false)
          setStreamingComplete(true)
          setShowToast(true)
          setTimeout(() => {
            setShowToast(false)
          }, 3000)
        } else if (newStep === 'FAILED') {
          setIsFailed(true)
          // 研究结束时关闭loading并显示toast
          setIsGlobalLoading(false)
          setStreamingComplete(true)
        } else {
          // 其他状态都保持loading
          setIsGlobalLoading(true)
        }

        // 更新当前研究状态
        setResearchStatus(newStep)
      }

      // 添加消息到状态
      setResearchMessages((prev) => [...prev, messageData])
    } catch (error) {
      console.error(t('researchResults.errorOccurred'), error)
      setIsGlobalLoading(false) // 出错时关闭loading
      setActiveTask(t('researchResults.errorOccurred'))
    }
  }
  // useEffect(() => {
  //   console.log('researchMessages updated:', researchMessages)
  // }, [researchMessages])

  useWebSocketWithReconnection({
    onMessage: handleSocketMessage,
  })

  // 检查是否是从深度研究页面来的
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const hasHistory = window.history.length > 1
      const isFromDeepResearchPage = document.referrer.includes('/deep-research')
      setIsFromDeepResearch(hasHistory && isFromDeepResearchPage)
    }
  }, [])

  // 从路由参数中解析研究数据
  useEffect(() => {
    if (!router.isReady) return

    // 如果请求已经发送过，则不再重复发送
    if (requestSentRef.current) return

    try {
      const id = router.query.id as string
      if (!id) return

      // 从后端API获取任务数据
      const fetchTaskData = async () => {
        try {
          // 标记请求已发送
          requestSentRef.current = true

          console.log('Fetching task data, task_id:', id)
          setIsLoading(true)
          setActiveTask(t('researchResults.loadingTaskData'))

          const res = await authRequest.get(`${API_BASE_URL}/deep-research/task/${id}`)

          console.log('Task data fetch successful:', res.data.data)

          // 设置研究数据
          const taskData = res.data.data.task
          setResearchData({
            task_id: taskData.task_id,
            question: taskData.question,
            requirement: taskData.requirement || '',
          })

          // 在这里设置后端返回的历史数据应该就可以回显之前的研究结果
          // setResearchMessages(res.)

          researchDataRef.current = {
            task_id: taskData.task_id,
            question: taskData.question,
            requirement: taskData.requirement || '',
          }

          // 设置大纲数据
          if (taskData.outline && Array.isArray(taskData.outline)) {
            setOutline(taskData.outline)
            outlineRef.current = taskData.outline
          }

          if (taskData.record) {
            for (const msg of taskData.record) {
              setResearchMessages((prev) => [...prev, msg])
              if (msg.STEP === 'END') {
                // 研究结束时关闭loading并显示toast
                setIsGlobalLoading(false)
                setStreamingComplete(true)
                setShowToast(true)
                setTimeout(() => {
                  setShowToast(false)
                }, 3000)
              }
            }
          }

          setIsLoading(false)
        } catch (error) {
          console.error(t('researchResults.errorOccurred'), error)
          setIsLoading(false)
          setActiveTask(t('researchResults.errorOccurred'))
        }
      }

      fetchTaskData()
    } catch (error) {
      console.error('Error parsing research data:', error)
    }
  }, [router.isReady, router.query, t])

  // 在大纲渲染完成后开始流式请求
  // useEffect(() => {
  //   if (
  //     !researchData ||
  //     !researchData.task_id ||
  //     streamRequestSentRef.current ||
  //     outline.length === 0
  //   )
  //     return

  //   const fetchResearchStream = async () => {
  //     try {
  //       streamRequestSentRef.current = true
  //       console.log(`Starting research content stream request, task_id: ${researchData.task_id}`)
  //       setActiveTask(t('researchResults.processingWait'))
  //       setIsGlobalLoading(true) // 开始流式请求时激活全局loading

  //       const response = await fetch(`${API_BASE_URL}/api/research/${researchData.task_id}`)

  //       if (!response.ok || !response.body) {
  //         throw new Error(t('error.netError'))
  //       }

  //       setResearchMessages([]) // 清空消息
  //       setResearchStatus('START') // 初始化研究状态

  //       console.log('Reading stream data')
  //       const reader = response.body.getReader()
  //       const decoder = new TextDecoder()
  //       let buffer = ''

  //       // eslint-disable-next-line no-constant-condition
  //       while (true) {
  //         const { done, value } = await reader.read()

  //         if (done) {
  //           console.log('Stream reading complete')
  //           setStreamingComplete(true)
  //           // 如果没有收到END消息，但流已结束，也关闭loading
  //           setIsGlobalLoading(false)
  //           setActiveTask(t('researchResults.allCompleted'))
  //           break
  //         }

  //         // 解码接收到的数据
  //         const textChunk = decoder.decode(value, { stream: true })
  //         buffer += textChunk

  //         // 处理buffer中的完整消息
  //         const lines = buffer.split('\n')
  //         buffer = lines.pop() || '' // 保留最后一个可能不完整的消息

  //         for (const line of lines) {
  //           if (line.trim().startsWith('data:')) {
  //             try {
  //               // 提取JSON部分
  //               const jsonString = line.substring(5).trim()
  //               if (jsonString) {
  //                 const messageData = JSON.parse(jsonString) as ResearchMessage

  //                 // 立即处理状态消息
  //                 if (
  //                   messageData.ROLE === 'WORKFLOW' &&
  //                   messageData.TYPE === 'STATUS' &&
  //                   messageData.STEP
  //                 ) {
  //                   const newStep = messageData.STEP

  //                   // 每个状态转换都需要保持loading状态，除非是END
  //                   if (newStep === 'END') {
  //                     // 研究结束时关闭loading并显示toast
  //                     setIsGlobalLoading(false)
  //                     setStreamingComplete(true)
  //                     setShowToast(true)
  //                     setTimeout(() => {
  //                       setShowToast(false)
  //                     }, 3000)
  //                   } else {
  //                     // 其他状态都保持loading
  //                     setIsGlobalLoading(true)
  //                   }

  //                   // 更新当前研究状态
  //                   setResearchStatus(newStep)
  //                 }

  //                 // 添加消息到状态
  //                 setResearchMessages((prev) => [...prev, messageData])
  //               }
  //             } catch (err) {
  //               console.warn('Error parsing message:', line, err)
  //             }
  //           }
  //         }
  //       }
  //     } catch (error) {
  //       console.error(t('researchResults.errorOccurred'), error)
  //       setIsGlobalLoading(false) // 出错时关闭loading
  //       setActiveTask(t('researchResults.errorOccurred'))
  //     }
  //   }

  //   fetchResearchStream()
  // }, [outline, researchData, t])

  // 更新研究状态 - 简化版本，只用于查找最新状态
  const updateResearchStatus = () => {
    // 遍历所有消息，寻找最新的WORKFLOW和STATUS类型消息
    const statusMessages = researchMessages.filter(
      (message) => message.ROLE === 'WORKFLOW' && message.TYPE === 'STATUS',
    )

    if (statusMessages.length === 0) return

    // 获取最后一个状态消息的状态
    const lastStatusMessage = statusMessages[statusMessages.length - 1]
    if (lastStatusMessage.STEP) {
      setResearchStatus(lastStatusMessage.STEP)
    }
  }

  // 内容更新时更新章节内容映射
  useEffect(() => {
    // 更新章节内容映射
    updateChapterContents()
    // 更新章节活跃状态
    updateChapterMarkdownActive()
    // 更新研究报告内容
    updateReportContent()
    // 更新研究状态
    updateResearchStatus()
  }, [researchMessages])

  // 更新章节内容映射
  const updateChapterContents = () => {
    const newChapterContents = new Map<string, ResearchMessage[]>()
    const newChapterContentItems = new Map<string, ChapterContentItem[]>()

    // 先按章节分组所有消息
    researchMessages.forEach((message) => {
      if (message.CONTENT?.CHAPTER) {
        const chapter = message.CONTENT.CHAPTER
        const messages = newChapterContents.get(chapter) || []
        messages.push(message)
        newChapterContents.set(chapter, messages)
      }
    })

    // 处理每个章节的消息，将普通消息和Markdown内容交替排列
    newChapterContents.forEach((messages, chapter) => {
      const items: ChapterContentItem[] = []

      // 先处理非REPORTING类型的消息
      const regularMessages = messages.filter(
        (msg) =>
          !(
            msg.ROLE === 'RESEARCHER' &&
            msg.TYPE === 'REPORTING' &&
            ['START', 'RUNNING'].includes(msg.STEP)
          ),
      )

      // 将普通消息添加到内容项列表
      regularMessages.forEach((msg) => {
        items.push({
          type: 'message',
          message: msg,
        })
      })

      // 处理REPORTING类型的消息，合并成Markdown内容
      const reportingMessages = messages.filter(
        (msg) =>
          msg.ROLE === 'RESEARCHER' &&
          msg.TYPE === 'REPORTING' &&
          ['START', 'RUNNING'].includes(msg.STEP),
      )

      let markdownContent = ''
      let isStreaming = false

      // 合并所有REPORTING消息的内容
      reportingMessages.forEach((msg) => {
        if (msg.CONTENT?.MESSAGE) {
          markdownContent = msg.CONTENT.MESSAGE
        }
        // 如果有任何一条REPORTING消息的STEP不是END，则认为内容仍在流式传输
        if (msg.STEP !== 'END') {
          isStreaming = true
        }
      })

      // 如果有Markdown内容，则添加到内容项列表
      if (markdownContent) {
        items.push({
          type: 'markdown',
          content: markdownContent,
          isStreaming: isStreaming,
        })
      }

      // 更新章节内容项映射
      newChapterContentItems.set(chapter, items)
    })

    setChapterContents(newChapterContents)
    setChapterContentItems(newChapterContentItems)
  }

  // 更新章节Markdown活跃状态
  const updateChapterMarkdownActive = () => {
    // 只保留活跃状态映射
    const newChapterMarkdownActive = new Map<string, boolean>()

    // 保存每个章节最后一个消息的状态
    const chapterLastSteps = new Map<string, string>()

    // 按章节分组所有REPORTING类型消息
    researchMessages.forEach((message) => {
      if (
        message.ROLE === 'RESEARCHER' &&
        message.TYPE === 'REPORTING' &&
        message.CONTENT?.CHAPTER
      ) {
        const chapter = message.CONTENT.CHAPTER

        // 记录每个章节的最后一个消息步骤
        chapterLastSteps.set(chapter, message.STEP)
      }
    })

    // 设置每个章节的活跃状态
    chapterLastSteps.forEach((step, chapter) => {
      // 设置活跃状态 - 如果最后一个消息不是END，则为活跃状态
      const isActive = step !== 'END' && !isFailed
      newChapterMarkdownActive.set(chapter, isActive)
    })

    // 只更新活跃状态映射
    setChapterMarkdownActive(newChapterMarkdownActive)
  }

  const handleDownloadPrice = () => {
    paddlePayRef.current?.openCheckout({
      priceId: process.env.NEXT_PUBLIC_PADDLE_DEEP_RESEARCH_PRICE_ID || '',
    })
  }

  const handleTaskDownload = async () => {
    try {
      const res = await authRequest.post(`${API_BASE_URL}/deep-research/taskDownload`, {
        taskId: researchData.task_id,
        useReward: true,
      })

      console.log('Task download request successful:', res.data)
      return res.data
    } catch (error) {
      console.error('check download available error:', error)
      return false
    }
  }
  // 更新研究报告内容
  const updateReportContent = () => {
    let content = ''
    let isStreaming = false
    let hasReporterMessages = false

    // 按消息顺序处理所有REPORTER类型消息
    researchMessages.forEach((message) => {
      if (message.ROLE === 'REPORTER' && message.TYPE === 'REPORTING' && message.CONTENT?.MESSAGE) {
        hasReporterMessages = true
        if (message.STEP === 'RUNNING') {
          content += message.CONTENT.MESSAGE
        }

        // 如果有任何一条消息的STEP不是END，则认为报告还在流式传输中
        if (message.STEP !== 'END') {
          isStreaming = true
        }
      }
    })

    if (hasReporterMessages) {
      setReportContent(content)
      setIsReportStreaming(isStreaming)
      setTimeout(() => {
        const reportContainer = document.querySelector('.report-container')
        if (reportContainer) {
          reportContainer.scrollTop = reportContainer.scrollHeight * 10
        }
      }, 2000)
    }
  }

  // 获取所有章节索引（用于初始渲染章节等待区）
  const getAllChapterIndices = (items: OutlineItem[]): string[] => {
    let indices: string[] = []

    items.forEach((item) => {
      if (item.chapterIndex) {
        indices.push(item.chapterIndex)
      }

      if (item.children && item.children.length > 0) {
        indices = [...indices, ...getAllChapterIndices(item.children)]
      }
    })

    return indices
  }

  const { exportToWord } = useDownloadMarkdown(
    getAllChapterIndices(outline).reduce((acc, curr) => {
      const contentItems = chapterContentItems.get(curr) || []
      const markdownContent =
        contentItems.filter((item) => item.type === 'markdown').pop()?.content || ''
      return acc + markdownContent
    }, ''),
  )

  // 根据章节索引查找章节标题
  const findChapterTitle = (chapterIndex: string, items: OutlineItem[]): string => {
    for (const item of items) {
      if (item.chapterIndex === chapterIndex) {
        return item.title
      }

      if (item.children && item.children.length > 0) {
        const title = findChapterTitle(chapterIndex, item.children)
        if (title) return title
      }
    }

    return ''
  }

  // 渲染准备阶段消息
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const renderPreparationMessages = () => {
    const preparationMessages = researchMessages.filter(
      (message) =>
        (message.ROLE === 'WORKFLOW' && message.STEP === 'START') ||
        (message.TYPE === 'THINKING' && !message.CONTENT?.CHAPTER),
    )

    if (preparationMessages.length === 0) return null

    return (
      <div className='mb-6 rounded-lg border border-gray-200 bg-gray-50 p-4'>
        <h3 className='mb-3 text-lg font-medium text-gray-700'>
          {t('researchResults.requirementAnalysis')}
        </h3>
        <div className='space-y-2'>
          {preparationMessages.map((message, index) => renderMessage(message, index))}
        </div>
      </div>
    )
  }

  // 添加滚动到指定章节的函数
  const scrollToChapter = (chapterIndex: string) => {
    // 转义章节索引中的特殊字符
    const escapedChapterIndex = chapterIndex.replace(/\./g, '-')

    // 滚动研究过程区域
    const processSection = document.querySelector(`#process-chapter-${escapedChapterIndex}`)
    if (processSection) {
      processSection.scrollIntoView({ behavior: 'smooth', block: 'start' })
    }

    // 滚动研究初稿区域
    const draftSection = document.querySelector(`#draft-chapter-${escapedChapterIndex}`)
    if (draftSection) {
      draftSection.scrollIntoView({ behavior: 'smooth', block: 'start' })
    }
  }

  // 渲染消息
  const renderMessage = (message: ResearchMessage, index: number) => {
    // 处理工作流开始或结束消息
    if (message.ROLE === 'WORKFLOW' && (message.STEP === 'START' || message.STEP === 'END')) {
      return (
        <div key={index} className='my-1.5 flex justify-center'>
          <div className='rounded-full bg-gray-100 px-2 py-0.5 text-xs text-gray-500'>
            {message.STEP === 'START'
              ? t('researchResults.startingAnalysis')
              : t('researchResults.completed')}
          </div>
        </div>
      )
    }

    // 处理思考类型消息
    if (message.ROLE === 'RESEARCHER' && message.TYPE === 'THINKING' && message.CONTENT?.MESSAGE) {
      return (
        <div
          key={index}
          className='mb-1.5 flex items-center gap-2 rounded border-l-2 border-indigo-200 bg-gray-50 px-2 py-1'>
          <div className='flex items-center gap-1.5'>
            <div className='rounded-full bg-indigo-100 px-1.5 py-0.5 text-xs font-medium text-indigo-800'>
              {message.CONTENT.ACTION ? message.CONTENT.ACTION : message.ROLE}
            </div>
            <span className='text-xs text-gray-700'>{message.CONTENT.MESSAGE}</span>
          </div>
        </div>
      )
    }

    // 默认消息样式, 只处理 RESEARCHER 的消息
    if (message.ROLE === 'RESEARCHER' && message.CONTENT?.MESSAGE) {
      return (
        <div
          key={index}
          className='mb-1.5 flex items-center gap-2 rounded border-l-2 border-gray-200 px-2 py-1'>
          <div className='flex items-center gap-1.5 whitespace-nowrap'>
            <div className='rounded-full bg-gray-100 px-1.5 py-0.5 text-xs font-medium text-gray-700'>
              {message.ROLE}
            </div>
            <div className='text-xs text-gray-500'>{message.STEP}</div>
            <span className='text-xs text-gray-700'>{message.CONTENT.MESSAGE}</span>
          </div>
        </div>
      )
    }

    return null
  }

  // 渲染大纲子项
  const renderOutlineItems = (items: OutlineItem[], indent = 0) => {
    return items.map((item) => {
      // 获取章节的状态
      const chapterStatus = researchMessages.find(
        (msg) =>
          msg.ROLE === 'RESEARCHER' &&
          msg.TYPE === 'REPORTING' &&
          msg.STEP === 'END' &&
          msg.CONTENT?.CHAPTER === item.chapterIndex,
      )
        ? 'END'
        : researchStatus

      // 根据状态设置颜色
      let statusColor = 'bg-primary'
      if (chapterStatus === 'END') {
        statusColor = 'text-primary-600'
      } else if (chapterStatus === 'START') {
        statusColor = 'bg-blue-600'
      } else if (chapterStatus === 'PLANNING') {
        statusColor = 'bg-purple-600'
      } else if (chapterStatus === 'RESEARCHING') {
        statusColor = 'bg-yellow-600'
      } else if (chapterStatus === 'DRAFTING') {
        statusColor = 'bg-orange-600'
      } else if (chapterStatus === 'REFLECTING') {
        statusColor = 'bg-teal-600'
      } else if (chapterStatus === 'REPORTING') {
        statusColor = 'bg-pink-600'
      }

      return (
        <div key={item.id} className='mb-3'>
          <div className='flex flex-col' style={{ marginLeft: `${indent * 16}px` }}>
            <button
              onClick={() => scrollToChapter(item.chapterIndex)}
              className='group flex items-center text-left text-sm text-indigo-700 transition-colors duration-200 hover:text-indigo-900'>
              <span>
                {item.chapterIndex} {item.title}
              </span>
              {!isFailed && (
                <div className='ml-2 flex items-center opacity-50'>
                  {chapterStatus === 'END' ? (
                    <svg
                      className={`h-5 w-5 ${statusColor}`}
                      xmlns='http://www.w3.org/2000/svg'
                      viewBox='0 0 20 20'
                      fill='currentColor'
                      aria-label={t(`researchResults.status.${chapterStatus}`)}>
                      <path
                        fillRule='evenodd'
                        d='M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z'
                        clipRule='evenodd'
                      />
                    </svg>
                  ) : (
                    <div className='relative flex items-center'>
                      <div className={`h-3 w-3 rounded-full ${statusColor}`}></div>
                      <div
                        className={`h-3 w-3 rounded-full ${statusColor} absolute left-0 top-0 animate-ping`}></div>
                      <span className='absolute -top-8 left-1/2 hidden -translate-x-1/2 whitespace-nowrap rounded bg-gray-800 px-2 py-1 text-xs text-white group-hover:block'>
                        {t(`researchResults.status.${chapterStatus}`)}
                      </span>
                    </div>
                  )}
                </div>
              )}
            </button>
          </div>
          {item.children && item.children.length > 0 && (
            <div className='mt-2'>{renderOutlineItems(item.children, indent + 1)}</div>
          )}
        </div>
      )
    })
  }

  // 渲染研究进度状态指示器
  const renderResearchStatusIndicator = () => {
    // 尝试获取翻译文本，如果不存在则使用进行中
    const statusText = t(`researchResults.status.${researchStatus}`, {
      defaultValue: t('researchResults.status.inProgress'),
    })

    // 根据状态设置颜色和动画
    let statusColor = 'bg-indigo-100 text-indigo-800'
    let isAnimated = true

    if (researchStatus === 'END') {
      statusColor = 'bg-green-100 text-green-800'
      isAnimated = false // 完成状态不需要动画
    } else if (researchStatus === 'START') {
      statusColor = 'bg-blue-100 text-blue-800'
    } else if (researchStatus === 'PLANNING') {
      statusColor = 'bg-purple-100 text-purple-800'
    } else if (researchStatus === 'RESEARCHING') {
      statusColor = 'bg-yellow-100 text-yellow-800'
    } else if (researchStatus === 'DRAFTING') {
      statusColor = 'bg-orange-100 text-orange-800'
    } else if (researchStatus === 'REFLECTING') {
      statusColor = 'bg-teal-100 text-teal-800'
    } else if (researchStatus === 'REPORTING') {
      statusColor = 'bg-pink-100 text-pink-800'
    } else if (researchStatus === 'FAILED') {
      statusColor = 'bg-red-100 text-red-800'
      isAnimated = false // 完成状态不需要动画
    }

    return (
      <div
        className={`inline-flex items-center rounded-full px-3 py-1 text-sm font-medium ${statusColor} ${isAnimated ? 'animate-pulse' : ''}`}>
        {isAnimated && (
          <div className='relative mr-2'>
            <div className='h-2 w-2 rounded-full bg-current opacity-75'></div>
            <div className='absolute left-0 top-0 h-2 w-2 animate-ping rounded-full bg-current opacity-75'></div>
          </div>
        )}
        {statusText}
      </div>
    )
  }

  // 渲染Toast提示
  const renderToast = () => {
    if (!showToast) return null

    return (
      <div className='animate-fadeIn fixed bottom-4 right-4 z-50 flex items-center rounded-lg border border-green-200 bg-green-50 p-4 shadow-lg'>
        <div className='mr-3 flex-shrink-0'>
          <svg
            className='h-5 w-5 text-green-600'
            xmlns='http://www.w3.org/2000/svg'
            viewBox='0 0 20 20'
            fill='currentColor'>
            <path
              fillRule='evenodd'
              d='M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z'
              clipRule='evenodd'
            />
          </svg>
        </div>
        <div className='text-sm font-medium text-green-800'>
          {t('researchResults.toast.completed', '研究报告已完成')}
        </div>
      </div>
    )
  }

  // 加载状态
  if (isLoading || !researchData) {
    return (
      <SimpleLayout fullWidth={true}>
        <div className='flex h-screen items-center justify-center'>
          <div className='text-center'>
            <div className='mx-auto h-12 w-12 animate-spin rounded-full border-b-2 border-t-2 border-indigo-500'></div>
            <p className='mt-4 text-lg text-gray-600'>{t('researchResults.loadingResults')}</p>
          </div>
        </div>
      </SimpleLayout>
    )
  }

  const downloadWord = () => {
    exportToWord(
      `${t('researchResults.researchDrafting')}.docx`,
      handleTaskDownload,
      handleDownloadPrice,
    )
  }

  const retryDeepResearch = async () => {
    try {
      // 标记请求已发送
      requestSentRef.current = true

      setIsFailed(false)
      setResearchStatus('inProgress')
      setStreamingComplete(false)
      setResearchMessages([])
      console.log('retry task, task_id:', researchDataRef.current?.task_id)
      setIsLoading(true)
      setActiveTask(t('researchResults.loadingTaskData'))
      const res = await authRequest.post(`${API_BASE_URL}/deep-research/task/retry`, {
        taskId: researchDataRef.current?.task_id,
      })

      console.log('Task data retry successful', researchDataRef.current?.task_id)

      setIsLoading(false)
    } catch (error) {
      console.error(t('researchResults.errorOccurred'), error)
      setIsLoading(false)
      setActiveTask(t('researchResults.errorOccurred'))
    }
  }

  const directDownload = () => {
    exportToWord(`${t('researchResults.researchDrafting')}.docx`)
  }
  return (
    <>
      <Head>
        <title>{`${researchData.question || t('researchResults.researchQuestion')} | AI Smarties`}</title>
        <meta name='description' content={t('deepResearch.description')} />
        <style>{fadeInAnimation}</style>
      </Head>

      <SimpleLayout fullWidth={true}>
        <div
          className='w-full'
          asm-tracking='VISIT_RESEARCH_RESULT_PAGE:VIEW'
          asm-tracking-p-page='RESULT'>
          {/* 顶部固定区域 */}
          <div className='sticky top-0 z-10 mb-6 bg-white px-4 pb-4 pt-2 shadow-sm'>
            {/* 返回按钮 */}
            <button
              onClick={() => {
                if (isFromDeepResearch) {
                  router.back()
                } else {
                  router.push('/deep-research?step=0')
                }
              }}
              className='mb-3 inline-flex items-center text-sm font-medium text-indigo-600 transition-transform duration-200 hover:scale-105 hover:text-indigo-800'
              aria-label={
                isFromDeepResearch
                  ? t('researchResults.goBack')
                  : t('researchResults.startNewResearch')
              }>
              <svg
                className='mr-1 h-4 w-4'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
                xmlns='http://www.w3.org/2000/svg'>
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth='2'
                  d='M10 19l-7-7m0 0l7-7m-7 7h18'></path>
              </svg>
              {isFromDeepResearch
                ? t('researchResults.goBack')
                : t('researchResults.startNewResearch')}
            </button>

            {/* 标题区域 */}
            <div className='border-gray-200 p-4'>
              <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between'>
                <h1 className='border-b text-2xl font-bold text-gray-900 sm:border-b-0 sm:text-3xl'>
                  {researchData.question || t('researchResults.researchQuestion')}
                </h1>
                {/* 研究状态指示器 */}
                <div className='mt-2 sm:ml-4 sm:mt-0'>{renderResearchStatusIndicator()}</div>
              </div>
              {researchData.requirement && (
                <p className='mt-2 text-sm text-gray-600'>{researchData.requirement}</p>
              )}
            </div>
          </div>

          {/* 主要内容区域 */}
          <div className='w-full'>
            {/* 上方提示条 */}
            {streamingComplete && !isFailed && (
              <div className='mb-6'>
                <DraftNotice />
              </div>
            )}

            {isMobile ? (
              <div className='relative w-full overflow-x-auto'>
                {/* <div className="fixed bottom-6 left-1/2 z-50 flex -translate-x-1/2 transform animate-bounce items-center rounded-full bg-indigo-600/90 px-4 py-2 text-sm font-medium text-white shadow-lg">
             <span>{t('researchResults.swipeHint')}</span>
            <svg 
            className="ml-2 h-4 w-4"
            xmlns="http://www.w3.org/2000/svg" 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
            >
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
      </svg>
    </div> */}
                <div className='pd-16 flex w-full space-x-4 p-4'>
                  {/* 右侧研究报告 - 草稿*/}
                  <div className='w-[calc(100vw-32px)] flex-shrink-0 rounded-lg border border-gray-100 bg-white p-4 shadow-lg transition-all duration-300 ease-in-out'>
                    <div className='flex items-center justify-between'>
                      <h2 className='mb-2 text-lg font-semibold text-gray-800'>
                        {t('researchResults.researchDrafting')}
                      </h2>
                      {streamingComplete && !isFailed && (
                        <button
                          className='rounded-md border border-transparent bg-primary px-3 py-1.5 text-xs font-medium text-white shadow-sm'
                          onClick={downloadWord}
                          asm-tracking='TEST_CLICK_DOWNLOAD_RESEARCH_RESULT_PAGE:CLICK'>
                          {t('deepResearch.download')}
                        </button>
                      )}
                    </div>

                    <div className='report-container'>
                      {!isFailed && outlineRef && outlineRef.current.length > 0 ? (
                        <div>
                          <div className='space-y-4'>
                            {getAllChapterIndices(outlineRef.current).map((chapterIndex) => {
                              const contentItems = chapterContentItems.get(chapterIndex) || []

                              // 获取该章节的markdown内容, 只取最后一次更新
                              const markdownContent =
                                contentItems.filter((item) => item.type === 'markdown').pop()
                                  ?.content || ''

                              return (
                                <div
                                  key={chapterIndex}
                                  id={`draft-chapter-${chapterIndex.replace(/\./g, '-')}`}
                                  className='chapter-section'>
                                  {markdownContent && (
                                    <ResearchMarkdownRenderer
                                      content={markdownContent}
                                      isStreaming={
                                        isGlobalLoading &&
                                        (researchStatus === 'RESEARCHING' ||
                                          researchStatus === 'DRAFTING' ||
                                          researchStatus === 'REFLECTING')
                                      }
                                      role='researcher'
                                      className='prose prose-sm prose-headings:text-sm prose-p:text-xs prose-ul:text-xs prose-ol:text-xs prose-li:text-xs prose-blockquote:text-xs prose-code:text-xs prose-pre:text-xs prose-strong:text-xs prose-em:text-xs'
                                    />
                                  )}
                                </div>
                              )
                            })}
                          </div>

                          {isGlobalLoading && (
                            <div className='sticky bottom-0 mt-4 flex items-center justify-center space-x-2 border-t border-gray-100 bg-white/80 py-3 backdrop-blur-sm'>
                              <div className='flex items-center space-x-2'>
                                <div className='h-3 w-3 animate-pulse rounded-full bg-indigo-500'></div>
                                <div className='h-3 w-3 animate-pulse rounded-full bg-indigo-500 delay-150'></div>
                                <div className='h-3 w-3 animate-pulse rounded-full bg-indigo-500 delay-300'></div>
                              </div>
                              <span className='ml-2 text-sm font-medium text-indigo-600'>
                                {t('researchResults.updating')}
                              </span>
                            </div>
                          )}
                        </div>
                      ) : isFailed ? (
                        <div className='flex min-h-[240px] flex-col items-center justify-center'>
                          <div className='mt-12 p-2'>
                            <img
                              src='/images/report_failed.png'
                              className='rounded-[4px]'
                              alt='AI webpage report failed image'
                            />
                          </div>
                          <div className='max-w-[240px] text-center'>
                            <h2 className='text-blank-500 mt-4 font-bold'>
                              {t('researchResults.researchFailedTitle')}
                            </h2>
                            <p className='mt-4 text-gray-500'>
                              {t('researchResults.researchFailed')}
                            </p>
                          </div>
                          <button
                            className='mx-auto mt-6 w-[240px] cursor-pointer rounded-md border border-transparent bg-primary px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2'
                            onClick={retryDeepResearch}
                            asm-tracking='TEST_CLICK_RETRY_RESEARCH_RESULT_PAGE:CLICK'>
                            {t('researchResults.retry')}
                          </button>
                        </div>
                      ) : (
                        <div className='flex min-h-[400px] items-center justify-center'>
                          <div className='text-center'>
                            <div className='mx-auto h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-indigo-500'></div>
                            <p className='mt-4 text-gray-500'>
                              {t('researchResults.processingWait')}
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* 滑动提示 */}
                <div className='bg-black/60 absolute bottom-4 left-1/2 -translate-x-1/2 rounded-full px-3 py-1 text-xs text-white'>
                  {t('researchResults.swipeHint')}
                </div>
              </div>
            ) : (
              <Split
                className='flex'
                sizes={[20, 40, 40, 0]}
                minSize={15}
                gutterSize={3}
                gutterStyle={() => ({
                  backgroundColor: '#e5e7eb',
                  width: '3px',
                  cursor: 'col-resize',
                })}>
                {/* 左侧大纲区域 */}
                <div className='rounded-lg border border-gray-100 bg-white p-6 shadow-lg transition-all duration-300 ease-in-out'>
                  <div className='prose max-w-none'>
                    <h2 className='mb-6 border-b border-gray-100 pb-2 text-lg font-semibold text-gray-800'>
                      {t('researchResults.outline')}
                    </h2>

                    <div className='max-h-[70vh] min-h-[500px] overflow-y-auto'>
                      {outlineRef.current && outlineRef.current.length > 0 ? (
                        <div className='space-y-2'>{renderOutlineItems(outlineRef.current)}</div>
                      ) : (
                        <p className='text-sm text-gray-500'>{t('display.noData')}</p>
                      )}
                    </div>
                  </div>
                </div>

                {/* 中间内容区 研究过程区 */}
                <div className='rounded-lg border border-gray-100 bg-white p-6 shadow-lg transition-all duration-300 ease-in-out'>
                  <div className='prose max-w-none'>
                    <h2 className='mb-2 border-b border-gray-100 pb-2 text-xl font-semibold text-gray-800'>
                      {t('researchResults.researchContent')}
                    </h2>

                    <div
                      ref={contentRef}
                      className='max-h-[70vh] min-h-[500px] overflow-y-auto pr-2'>
                      {researchMessages.length > 0 ? (
                        <div className='space-y-4'>
                          {getAllChapterIndices(outline).map((chapterIndex) => {
                            const chapterTitle = findChapterTitle(chapterIndex, outline)
                            const contentItems = chapterContentItems.get(chapterIndex) || []

                            // 如果章节没有内容，显示等待提示
                            if (contentItems.length === 0) {
                              return (
                                <div
                                  key={chapterIndex}
                                  className='rounded-lg border border-gray-200 p-4'>
                                  <h4 className='mb-2 font-medium text-indigo-700'>
                                    {chapterIndex} {chapterTitle}
                                  </h4>
                                  <div className='flex h-12 items-center justify-center rounded-md bg-gray-50'>
                                    <span className='text-sm text-gray-500'>
                                      {t('researchResults.waiting')}
                                    </span>
                                  </div>
                                </div>
                              )
                            }

                            // 确定是否显示章节loading指示器
                            const showLoading =
                              isGlobalLoading &&
                              (researchStatus === 'RESEARCHING' ||
                                researchStatus === 'DRAFTING' ||
                                researchStatus === 'REFLECTING')

                            return (
                              <div
                                key={chapterIndex}
                                id={`process-chapter-${chapterIndex.replace(/\./g, '-')}`}
                                className='rounded-lg border border-gray-200 p-4'>
                                <h4 className='mb-2 font-medium text-indigo-700'>
                                  {chapterIndex} {chapterTitle}
                                </h4>
                                <div className='space-y-2'>
                                  {contentItems.map(
                                    (item, index) =>
                                      item.type === 'message' &&
                                      item.message &&
                                      renderMessage(item.message, index),
                                  )}

                                  {/* 最后显示加载指示器 */}
                                  {showLoading && (
                                    <div className='mt-4 flex items-center space-x-2 py-2 pl-2 text-gray-400'>
                                      <div className='h-2 w-2 animate-pulse rounded-full bg-indigo-500'></div>
                                      <div className='h-2 w-2 animate-pulse rounded-full bg-indigo-500 delay-150'></div>
                                      <div className='h-2 w-2 animate-pulse rounded-full bg-indigo-500 delay-300'></div>
                                    </div>
                                  )}
                                </div>
                              </div>
                            )
                          })}
                        </div>
                      ) : (
                        <div className='flex min-h-[400px] items-center justify-center'>
                          <div className='text-center'>
                            <div className='mx-auto h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-indigo-500'></div>
                            <p className='mt-4 text-gray-500'>
                              {t('researchResults.processingWait')}
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* 右侧研究报告 - 草稿*/}
                <div className='rounded-lg border border-gray-100 bg-white p-6 shadow-lg transition-all duration-300 ease-in-out'>
                  <div className='flex items-center justify-between'>
                    <h2 className='mb-2 border-b border-gray-100 pb-2 text-xl font-semibold text-gray-800'>
                      {t('researchResults.researchDrafting')}
                    </h2>
                    {streamingComplete && !isFailed && (
                      <>
                        <button
                          className='-mt-6 cursor-pointer rounded-md border border-transparent bg-primary px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2'
                          onClick={downloadWord}
                          asm-tracking='TEST_CLICK_DOWNLOAD_RESEARCH_RESULT_PAGE:CLICK'>
                          {t('deepResearch.download')}
                        </button>
                        <PaddlePay
                          ref={paddlePayRef}
                          afterPayAction={directDownload}
                          taskId={researchDataRef.current?.task_id}
                        />
                      </>
                    )}
                  </div>

                  {/* 研究报告草稿区 */}
                  <div className='report-container max-h-[70vh] min-h-[500px] overflow-y-auto pr-2'>
                    {outline && outline.length > 0 && !isFailed ? (
                      <div>
                        <div className='space-y-4'>
                          {getAllChapterIndices(outline).map((chapterIndex) => {
                            const chapterTitle = findChapterTitle(chapterIndex, outline)
                            const contentItems = chapterContentItems.get(chapterIndex) || []

                            // 获取该章节的markdown内容, 只取最后一次更新
                            const markdownContent =
                              contentItems.filter((item) => item.type === 'markdown').pop()
                                ?.content || ''

                            return (
                              <div
                                key={chapterIndex}
                                id={`draft-chapter-${chapterIndex.replace(/\./g, '-')}`}
                                className='chapter-section'>
                                {markdownContent && (
                                  <ResearchMarkdownRenderer
                                    content={markdownContent}
                                    isStreaming={
                                      isGlobalLoading &&
                                      (researchStatus === 'RESEARCHING' ||
                                        researchStatus === 'DRAFTING' ||
                                        researchStatus === 'REFLECTING')
                                    }
                                    role='researcher'
                                    className='prose prose-sm prose-headings:text-sm prose-p:text-xs prose-ul:text-xs prose-ol:text-xs prose-li:text-xs prose-blockquote:text-xs prose-code:text-xs prose-pre:text-xs prose-strong:text-xs prose-em:text-xs'
                                  />
                                )}
                              </div>
                            )
                          })}
                        </div>

                        {isGlobalLoading && (
                          <div className='sticky bottom-0 mt-4 flex items-center justify-center space-x-2 border-t border-gray-100 bg-white/80 py-3 backdrop-blur-sm'>
                            <div className='flex items-center space-x-2'>
                              <div className='h-3 w-3 animate-pulse rounded-full bg-indigo-500'></div>
                              <div className='h-3 w-3 animate-pulse rounded-full bg-indigo-500 delay-150'></div>
                              <div className='h-3 w-3 animate-pulse rounded-full bg-indigo-500 delay-300'></div>
                            </div>
                            <span className='ml-2 text-sm font-medium text-indigo-600'>
                              {t('researchResults.updating')}
                            </span>
                          </div>
                        )}
                      </div>
                    ) : isFailed ? (
                      <div className='flex min-h-[240px] flex-col items-center justify-center'>
                        <div className='mt-12 p-2'>
                          <img
                            src='/images/report_failed.png'
                            className='rounded-[4px]'
                            alt='AI webpage report failed image'
                          />
                        </div>
                        <div className='max-w-[240px] text-center'>
                          <h2 className='text-blank-500 mt-4 font-bold'>
                            {t('researchResults.researchFailedTitle')}
                          </h2>
                          <p className='mt-4 text-gray-500'>
                            {t('researchResults.researchFailed')}
                          </p>
                        </div>
                        <button
                          className='mx-auto mt-6 w-[240px] cursor-pointer rounded-md border border-transparent bg-primary px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2'
                          onClick={retryDeepResearch}
                          asm-tracking='TEST_CLICK_RETRY_RESEARCH_RESULT_PAGE:CLICK'>
                          {t('researchResults.retry')}
                        </button>
                      </div>
                    ) : (
                      <div className='flex min-h-[400px] items-center justify-center'>
                        <div className='text-center'>
                          <div className='mx-auto h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-indigo-500'></div>
                          <p className='mt-4 text-gray-500'>
                            {t('researchResults.processingWait')}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* 右侧研究报告 - 终稿 - 暂时先隐藏 */}
                {/*<div className='rounded-lg border border-gray-100 bg-white p-6 shadow-lg transition-all duration-300 ease-in-out'>*/}
                {/*  <h2 className='mb-2 border-b border-gray-100 pb-2 text-xl font-semibold text-gray-800'>*/}
                {/*    {t('researchResults.researchReport')}*/}
                {/*  </h2>*/}

                {/*  /!* 研究报告区 *!/*/}
                {/*  <div className='min-h-[500px] max-h-[70vh] overflow-y-auto pr-2 report-container'>*/}
                {/*    {renderReportContent()}*/}
                {/*  </div>*/}
                {/*</div>*/}
              </Split>
            )}

            {/* 下方提示条 */}
            {streamingComplete && !isFailed && (
              <div className='mt-6'>
                <DraftNotice />
              </div>
            )}
          </div>

          {/* Toast消息 */}
          {renderToast()}
        </div>
      </SimpleLayout>
    </>
  )
}

export default ResearchResultsPage
