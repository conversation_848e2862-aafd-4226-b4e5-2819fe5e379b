/* eslint-disable @typescript-eslint/no-explicit-any */
import { fetchWithAuth } from '@/lib/fetch'
const apiUrl = process.env.NEXT_PUBLIC_API_URL

export interface InitRequirementRequest {
  data: {
    requirement: {
      原始问题: string
      合规判定: boolean
      用户语言: string
      问题确认: string
      拒绝原因?: string
    }
    task_id: string
  }
  message: string
  success: boolean
}
// 获取输入问题的扩写
export const initRequirementApi = async (data: { question: string; user_language: string }) => {
  const response = await fetchWithAuth<Promise<InitRequirementRequest>>(
    `${apiUrl}/deep-research/init-requirement`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...data,
      }),
    },
  )

  return response
}

export const confirmRequirementApi = async (data: {
  confirmation: string
  taskId: string
  user_language: string
  question: string
}) => {
  const response = await fetchWithAuth<
    Promise<{
      message: string
      success: boolean
    }>
  >(`${apiUrl}/deep-research/confirm-requirement`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}

// api/deep-research/startTask
export const startTaskApi = async (data: {
  messages: {
    content: string
    role: 'user'
  }[]
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  outline: any
  question: string
  requirement: string
  task_id: string
  user_language: string
}) => {
  const response = await fetchWithAuth(`${apiUrl}/deep-research/startTask`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}

// deep-research/task/86070d6c-c456-4be4-a7d5-e260ad6f7044
export type StartTaskRequest = {
  is_free_download: boolean
  task: {
    createdAt: number
    outline: any
    question: string
    record: any
    requirement: string
    status: string
    task_id: string
    updatedAt: number
    user_id: string
    user_language: string
  }
}
export const getTaskStatusApi = async (data: { task_id: string }) => {
  const response = await fetchWithAuth<
    Promise<{
      message: string
      success: boolean
      data: StartTaskRequest
    }>
  >(`${apiUrl}/deep-research/task/${data.task_id}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  })

  return response
}

// deep-research/taskDownload
export const taskDownloadApi = async (data: { task_id: string; useReward: boolean }) => {
  const response = await fetchWithAuth<
    Promise<{
      message: string
      success: boolean
      data: {
        download_url: string
      }
    }>
  >(`${apiUrl}/deep-research/taskDownload`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
      useReward: true,
    }),
  })

  return response
}

// deep-research/task/retry
export const taskRetryApi = async (data: { task_id: string }) => {
  const response = await fetchWithAuth<
    Promise<{
      message: string
      success: boolean
    }>
  >(`${apiUrl}/deep-research/task/retry`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      ...data,
    }),
  })

  return response
}
